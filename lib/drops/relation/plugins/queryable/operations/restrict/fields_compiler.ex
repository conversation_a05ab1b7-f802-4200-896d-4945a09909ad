defmodule Drops.Relation.Plugins.Queryable.Operations.Restrict.FieldsCompiler do
  use Drops.Relation.Plugins.Queryable.Operations.Compiler

  defmodule Result do
    defstruct [:query, errors: []]

    def new(query), do: %__MODULE__{query: query}

    def to_success(result), do: {:ok, result.query}

    def to_error(result), do: {:error, Enum.reverse(result.errors)}
  end

  @spec visit(map(), map()) :: {:ok, Ecto.Query.t()} | {:error, [String.t()]}
  def visit(relation, %{query: query, opts: opts}) when is_list(opts) do
    schema = relation.schema

    result =
      Enum.reduce(opts[:restrict], Result.new(query), fn {key, value}, result ->
        case visit({schema[key], value}, %{query: result.query}) do
          {:error, :field_not_found} ->
            # Skip invalid field names instead of erroring
            result

          {:error, error} ->
            %{result | errors: [error | result.errors]}

          updated_query ->
            %{result | query: updated_query}
        end
      end)

    if result.errors == [], do: Result.to_success(result), else: Result.to_error(result)
  end

  def visit({nil, _value}, _opts), do: {:error, :field_not_found}

  def visit({field, value}, %{query: query}) when is_list(value) do
    where(query, [r], field(r, ^field.name) in ^value)
  end

  def visit({%{meta: %{nullable: true}} = field, nil}, %{query: query}) do
    where(query, [r], is_nil(field(r, ^field.name)))
  end

  def visit({%{meta: %{nullable: false}} = field, nil}, _opts) do
    {:error, build_error(:not_nullable, field.name)}
  end

  def visit({field, value}, %{query: query}) when is_boolean(value) do
    case is_boolean_field?(field) do
      true ->
        where(query, [r], field(r, ^field.name) == ^value)

      false ->
        {:error, build_error(:not_boolean_field, %{field: field.name, value: value})}
    end
  end

  def visit({field, value}, %{query: query}) do
    where(query, [r], field(r, ^field.name) == ^value)
  end
end
