defmodule Drops.Relation.Plugins.Queryable.Operations.Order.FieldsCompiler do
  use Drops.Relation.Plugins.Queryable.Operations.Compiler

  defmodule Result do
    defstruct [:query, errors: []]

    def new(query), do: %__MODULE__{query: query}

    def to_success(result), do: {:ok, result.query}

    def to_error(result), do: {:error, Enum.reverse(result.errors)}
  end

  @spec visit(map(), map()) :: {:ok, Ecto.Query.t()} | {:error, [String.t()]}
  def visit(%{schema: schema}, %{query: query, opts: opts}) when is_list(opts) do
    result =
      Enum.reduce(opts[:order], Result.new(query), fn value, result ->
        case visit(value, %{schema: schema, query: result.query}) do
          {:error, error} ->
            %{result | errors: [error | result.errors]}

          updated_query ->
            %{result | query: updated_query}
        end
      end)

    if result.errors == [], do: Result.to_success(result), else: Result.to_error(result)
  end

  def visit(names, %{query: query}) when is_list(names) do
    order_by(query, [{:asc, ^names}])
  end

  def visit({direction, name}, %{schema: schema} = opts) when direction in [:asc, :desc] do
    visit({schema[name], direction}, opts)
  end

  def visit(name, %{schema: schema} = opts) when is_atom(name) do
    visit(schema[name], opts)
  end

  def visit({nil, _value}, _opts), do: {:error, :field_not_found}
  def visit(nil, _opts), do: {:error, :field_not_found}

  def visit(%{name: name}, %{query: query}) do
    order_by(query, [{:asc, ^name}])
  end

  def visit({%{name: name}, direction}, %{query: query}) do
    order_by(query, [{^direction, ^name}])
  end
end
