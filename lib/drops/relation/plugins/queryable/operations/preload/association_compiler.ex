defmodule Drops.Relation.Plugins.Queryable.Operations.Preload.AssociationCompiler do
  use Drops.Relation.Plugins.Queryable.Operations.Compiler

  defmodule Result do
    defstruct [:query, errors: []]

    def new(query), do: %__MODULE__{query: query}

    def to_success(result), do: {:ok, result.query}

    def to_error(result), do: {:error, Enum.reverse(result.errors)}
  end

  @spec visit(map(), map()) :: {:ok, Ecto.Query.t()} | {:error, [String.t()]}
  def visit(relation, %{query: query, opts: opts}) when is_list(opts) do
    preload_opts = Keyword.get(opts, :preload, [])
    preload_value = Keyword.get(preload_opts, :preload)

    if preload_value do
      available_associations = get_available_associations(relation)

      result =
        case preload_value do
          association when is_atom(association) ->
            case visit({association in available_associations, association}, %{query: query}) do
              {:error, error} ->
                %Result{query: query, errors: [error]}

              updated_query ->
                Result.new(updated_query)
            end

          associations when is_list(associations) ->
            Enum.reduce(associations, Result.new(query), fn assoc_spec, result ->
              case assoc_spec do
                association when is_atom(association) ->
                  case visit({association in available_associations, association}, %{
                         query: result.query
                       }) do
                    {:error, error} ->
                      %{result | errors: [error | result.errors]}

                    updated_query ->
                      %{result | query: updated_query}
                  end

                {association, nested} when is_atom(association) ->
                  case visit({association in available_associations, {association, nested}}, %{
                         query: result.query
                       }) do
                    {:error, error} ->
                      %{result | errors: [error | result.errors]}

                    updated_query ->
                      %{result | query: updated_query}
                  end

                invalid ->
                  error =
                    build_error(:custom, "invalid preload specification: #{inspect(invalid)}")

                  %{result | errors: [error | result.errors]}
              end
            end)

          invalid ->
            error = build_error(:custom, "invalid preload value: #{inspect(invalid)}")
            %Result{query: query, errors: [error]}
        end

      if result.errors == [], do: Result.to_success(result), else: Result.to_error(result)
    else
      {:ok, query}
    end
  end

  def visit({false, association}, _opts) when is_atom(association) do
    {:error, build_error(:custom, "association :#{association} is not defined")}
  end

  def visit({false, {association, _nested}}, _opts) when is_atom(association) do
    {:error, build_error(:custom, "association :#{association} is not defined")}
  end

  def visit({true, association}, %{query: query}) when is_atom(association) do
    from(q in query, preload: ^association)
  end

  def visit({true, {association, nested}}, %{query: query}) when is_atom(association) do
    from(q in query, preload: ^[{association, nested}])
  end

  defp get_available_associations(relation) do
    relation_module = relation.__struct__

    if function_exported?(relation_module, :__schema_module__, 0) do
      schema_module = relation_module.__schema_module__()

      if function_exported?(schema_module, :__schema__, 1) do
        schema_module.__schema__(:associations)
      else
        []
      end
    else
      []
    end
  end
end
